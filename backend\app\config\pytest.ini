[tool:pytest]
# pytest配置文件

# 最小版本要求
minversion = 6.0

# 测试目录
testpaths = 
    generated_tests
    tests

# 添加选项
addopts = 
    -v
    --tb=short
    --strict-markers
    --strict-config
    --disable-warnings
    --alluredir=reports/allure-results
    --html=reports/report.html
    --self-contained-html
    --json-report
    --json-report-file=reports/report.json

# 标记定义
markers =
    api: API接口测试
    smoke: 冒烟测试
    regression: 回归测试
    integration: 集成测试
    functional: 功能测试
    performance: 性能测试
    security: 安全测试
    boundary: 边界值测试
    error: 错误场景测试
    slow: 慢速测试
    fast: 快速测试
    critical: 关键测试
    high: 高优先级测试
    medium: 中优先级测试
    low: 低优先级测试
    p0: P0级别测试
    p1: P1级别测试
    p2: P2级别测试
    p3: P3级别测试

# 过滤警告
filterwarnings =
    ignore::UserWarning
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning

# 日志配置
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

log_file = logs/pytest.log
log_file_level = DEBUG
log_file_format = %(asctime)s [%(levelname)8s] %(filename)s:%(lineno)d: %(message)s
log_file_date_format = %Y-%m-%d %H:%M:%S

# 自动使用的插件
required_plugins =
    allure-pytest>=2.8.0
    pytest-html>=3.0.0
    pytest-json-report>=1.4.0

# 收集配置
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*

# 超时配置
timeout = 300
timeout_method = thread

# 并行执行配置 (需要pytest-xdist)
# -n auto 表示自动检测CPU核心数
# -n 4 表示使用4个进程

# 重试配置 (需要pytest-rerunfailures)
# --reruns 3 表示失败时重试3次
# --reruns-delay 1 表示重试间隔1秒

# 覆盖率配置 (需要pytest-cov)
# --cov=app 表示检查app模块的覆盖率
# --cov-report=html 表示生成HTML覆盖率报告
