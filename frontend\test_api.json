{"openapi": "3.0.0", "info": {"title": "测试API", "version": "1.0.0", "description": "这是一个用于测试文档上传功能的API文档"}, "servers": [{"url": "https://api.example.com", "description": "测试服务器"}], "paths": {"/api/users": {"get": {"summary": "获取用户列表", "description": "获取系统中的用户列表", "parameters": [{"name": "page", "in": "query", "schema": {"type": "integer", "default": 1}, "description": "页码"}, {"name": "limit", "in": "query", "schema": {"type": "integer", "default": 10}, "description": "每页数量"}], "responses": {"200": {"description": "成功响应", "content": {"application/json": {"schema": {"type": "object", "properties": {"users": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "name": {"type": "string"}, "email": {"type": "string"}}}}, "total": {"type": "integer"}}}}}}}}, "post": {"summary": "创建用户", "description": "创建一个新用户", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string"}, "email": {"type": "string"}, "password": {"type": "string"}}, "required": ["name", "email", "password"]}}}}, "responses": {"201": {"description": "创建成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "integer"}, "name": {"type": "string"}, "email": {"type": "string"}}}}}}}}}}}